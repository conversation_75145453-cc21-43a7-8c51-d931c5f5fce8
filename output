Compiling 4 files with 0.8.23
Solc 0.8.23 finished in 3.77s
Compiler run [32msuccessful![0m

Ran 14 tests for test/real_testing/BenchMarkingStorage.sol:BenchMarkTest
[32m[PASS][0m testBenchmarkBuySingleMultiplePriceDifferentUser() (gas: 3355066)
Logs:
  LIMIT BUY ORDER DIFFERENT PRICE DIFFERENT USER : 
  ITERATION  1  GAS USED:  252038
  ITERATION  2  GAS USED:  174114
  ITERATION  3  GAS USED:  174114
  ITERATION  4  GAS USED:  174114
  ITERATION  5  GAS USED:  196238
  ITERATION  6  GAS USED:  174114
  ITERATION  7  GAS USED:  174114
  ITERATION  8  GAS USED:  174114
  ITERATION  9  GAS USED:  174114
  ITERATION  10  GAS USED:  174114

[32m[PASS][0m testBenchmarkBuySingleSamePriceDifferentUser() (gas: 3083535)
Logs:
  LIMIT BUY ORDERS SAME PRICE DIFFERENT USER : 
  ITERATION  1  GAS USED:  252038
  ITERATION  2  GAS USED:  146631
  ITERATION  3  GAS USED:  146631
  ITERATION  4  GAS USED:  146631
  ITERATION  5  GAS USED:  146631
  ITERATION  6  GAS USED:  146631
  ITERATION  7  GAS USED:  146631
  ITERATION  8  GAS USED:  146631
  ITERATION  9  GAS USED:  146631
  ITERATION  10  GAS USED:  146631

[32m[PASS][0m testBenchmarkCancelBuyOrders() (gas: 2306110)
Logs:
  CANCEL BUY ORDERS 1LO : 
  ITERATION  1  GAS USED:  68259
  ITERATION  2  GAS USED:  51159
  ITERATION  3  GAS USED:  51160
  ITERATION  4  GAS USED:  51159
  ITERATION  5  GAS USED:  51159
  ITERATION  6  GAS USED:  51160
  ITERATION  7  GAS USED:  51160
  ITERATION  8  GAS USED:  51160
  ITERATION  9  GAS USED:  51160
  ITERATION  10  GAS USED:  57260

[32m[PASS][0m testBenchmarkCancelSellOrders() (gas: 2300071)
Logs:
  CANCEL SELL ORDERS 1LO :
  ITERATION  1  GAS USED:  68043
  ITERATION  2  GAS USED:  50942
  ITERATION  3  GAS USED:  50942
  ITERATION  4  GAS USED:  50943
  ITERATION  5  GAS USED:  50943
  ITERATION  6  GAS USED:  50942
  ITERATION  7  GAS USED:  50942
  ITERATION  8  GAS USED:  50943
  ITERATION  9  GAS USED:  50943
  ITERATION  10  GAS USED:  57085

[32m[PASS][0m testBenchmarkMarketBuyDifferentPriceIterative() (gas: 12163275)
Logs:
  MARKET BUY 3PP 1LO:
  ITERATION  1  GAS USED:  247709
  ITERATION  2  GAS USED:  230688
  ITERATION  3  GAS USED:  230808
  ITERATION  4  GAS USED:  230800
  ITERATION  5  GAS USED:  228322
  ITERATION  6  GAS USED:  228004
  ITERATION  7  GAS USED:  230770
  ITERATION  8  GAS USED:  230698
  ITERATION  9  GAS USED:  230802
  ITERATION  10  GAS USED:  220758

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative1LO() (gas: 10938659)
Logs:
  MARKET BUY 1PP 1LO :
  ITERATION  1  GAS USED:  139261
  ITERATION  2  GAS USED:  137382
  ITERATION  3  GAS USED:  137382
  ITERATION  4  GAS USED:  137382
  ITERATION  5  GAS USED:  137382
  ITERATION  6  GAS USED:  137383
  ITERATION  7  GAS USED:  137383
  ITERATION  8  GAS USED:  137383
  ITERATION  9  GAS USED:  137383
  ITERATION  10  GAS USED:  137383

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative2LO() (gas: 11317190)
Logs:
  MARKET BUY 1PP 2LO :
  ITERATION  1  GAS USED:  175713
  ITERATION  2  GAS USED:  175712
  ITERATION  3  GAS USED:  175712
  ITERATION  4  GAS USED:  175712
  ITERATION  5  GAS USED:  175712
  ITERATION  6  GAS USED:  175713
  ITERATION  7  GAS USED:  175713
  ITERATION  8  GAS USED:  175713
  ITERATION  9  GAS USED:  175713
  ITERATION  10  GAS USED:  175713

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative3LO() (gas: 11678931)
Logs:
  MARKET BUY 1PP 3LO :
  ITERATION  1  GAS USED:  214164
  ITERATION  2  GAS USED:  214163
  ITERATION  3  GAS USED:  214163
  ITERATION  4  GAS USED:  214163
  ITERATION  5  GAS USED:  214163
  ITERATION  6  GAS USED:  214164
  ITERATION  7  GAS USED:  214164
  ITERATION  8  GAS USED:  214164
  ITERATION  9  GAS USED:  214164
  ITERATION  10  GAS USED:  209294

[32m[PASS][0m testBenchmarkMarketSellDifferentPriceIterative() (gas: 12102666)
Logs:
  MARKET BUY 3PP 1LO : 
  ITERATION  1  GAS USED :  230856
  ITERATION  2  GAS USED :  230858
  ITERATION  3  GAS USED :  230925
  ITERATION  4  GAS USED :  232752
  ITERATION  5  GAS USED :  228612
  ITERATION  6  GAS USED :  230885
  ITERATION  7  GAS USED :  230800
  ITERATION  8  GAS USED :  230824
  ITERATION  9  GAS USED :  231009
  ITERATION  10  GAS USED :  232638

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative1LO() (gas: 10980010)
Logs:
  MARKET SELL 1PP 1LO: 
  ITERATION  1  GAS USED:  138478
  ITERATION  2  GAS USED:  136752
  ITERATION  3  GAS USED:  136751
  ITERATION  4  GAS USED:  136752
  ITERATION  5  GAS USED:  136752
  ITERATION  6  GAS USED:  136752
  ITERATION  7  GAS USED:  136752
  ITERATION  8  GAS USED:  136752
  ITERATION  9  GAS USED:  136752
  ITERATION  10  GAS USED:  136752

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative2LO() (gas: 11351191)
Logs:
  MARKET SELL 1PP 2LO: 
  ITERATION  1  GAS USED:  174657
  ITERATION  2  GAS USED:  174657
  ITERATION  3  GAS USED:  174656
  ITERATION  4  GAS USED:  174657
  ITERATION  5  GAS USED:  174657
  ITERATION  6  GAS USED:  174657
  ITERATION  7  GAS USED:  174657
  ITERATION  8  GAS USED:  174657
  ITERATION  9  GAS USED:  174657
  ITERATION  10  GAS USED:  174657

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative3LO() (gas: 11709841)
Logs:
  MARKET SELL 1PP 3LO: 
  ITERATION  1  GAS USED:  212835
  ITERATION  2  GAS USED:  212835
  ITERATION  3  GAS USED:  212834
  ITERATION  4  GAS USED:  212835
  ITERATION  5  GAS USED:  212835
  ITERATION  6  GAS USED:  212835
  ITERATION  7  GAS USED:  212835
  ITERATION  8  GAS USED:  212835
  ITERATION  9  GAS USED:  212835
  ITERATION  10  GAS USED:  208404

[32m[PASS][0m testBenchmarkSellSingleDifferentPriceSameUser() (gas: 1927747)
Logs:
  LIMIT SELL ORDER DIFFERENT PP: 
  ITERATION  1 GAS USED:  256348
  ITERATION  2 GAS USED:  156178
  ITERATION  3 GAS USED:  178431
  ITERATION  4 GAS USED:  156177
  ITERATION  5 GAS USED:  156178
  ITERATION  6 GAS USED:  178428
  ITERATION  7 GAS USED:  156174
  ITERATION  8 GAS USED:  156178
  ITERATION  9 GAS USED:  178431
  ITERATION  10 GAS USED:  151378

[32m[PASS][0m testBenchmarkSellSingleSamePriceSameUser() (gas: 1810980)
Logs:
  LIMIT SELL ORDER 1PP: 
  ITERATION  1 GAS USED:  256250
  ITERATION  2 GAS USED:  150849
  ITERATION  3 GAS USED:  150849
  ITERATION  4 GAS USED:  150850
  ITERATION  5 GAS USED:  150849
  ITERATION  6 GAS USED:  150846
  ITERATION  7 GAS USED:  150847
  ITERATION  8 GAS USED:  150850
  ITERATION  9 GAS USED:  150850
  ITERATION  10 GAS USED:  146050

Suite result: [32mok[0m. [32m14[0m passed; [31m0[0m failed; [33m0[0m skipped; finished in 39.03ms (220.08ms CPU time)

Ran 1 test suite in 568.34ms (39.03ms CPU time): [32m14[0m tests passed, [31m0[0m failed, [33m0[0m skipped (14 total tests)
